import { expiryToSeconds, formatChartTimeframe } from '../../utils/formatter'
import type { PocketOption } from '../PocketOption'
import { logger } from '../../utils/logger'

export class SelectAsset {
	private static broker: PocketOption

	static init(broker: PocketOption) {
		if (!this.broker) {
			this.broker = broker
		}
	}

	/**
	 * @param assetSymbol The asset symbol to change to
	 * @param interval The interval if the chart timeframe, refer to `formatChartTimeframe` for available intervals
	 * @returns
	 */
	static async call(assetSymbol: string, interval: number): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const chartPeriodToString = formatChartTimeframe(interval)
			if (!chartPeriodToString) {
				return reject(new Error(`Invalid chart period: ${interval}`))
			}

			const period = expiryToSeconds(chartPeriodToString)

			this.broker.emit('changeSymbol', { asset: assetSymbol, period })
			resolve()
		})
	}
}
