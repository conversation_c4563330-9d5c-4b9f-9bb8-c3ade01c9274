import { io, type Socket } from 'socket.io-client'
import { Region } from '../constants'
import { extractAuth } from '../utils/parsers'
import { logger } from '../utils/logger'
import { expiryToSeconds, formatChartTimeframe, formatData } from '../utils/formatter'
import { Order } from './channels/order'
import { Candles } from './channels/candles'
import { SelectAsset } from './channels/selectAsset'
import { SignalEngine } from '../core/SignalEngine'
import { format } from 'morgan'

export class PocketOption {
	private ws?: Socket
	private balance: number = 0
	private isConnected: boolean = false
	private connectionTimeout: number = 10000 // 10 seconds
	private heartbeatInterval: NodeJS.Timeout | null = null
	private signalEngine: SignalEngine = new SignalEngine('SMA_CROSS')
	private candleMap = new Map<string, Candle>()

	private orderPayload: OrderPayload | null = null
	private orderResult: OrderResult | null = null
	private chartSettings: ChartSettings | null = null

	private currentCandle: Partial<Candle> | null = null
	private candleBufferInterval: number = 30 // in seconds
	private candleFlushTimer: NodeJS.Timeout | null = null

	private signalEngines: Map<number, SignalEngine> = new Map()
	private candleBuckets: Map<number, Partial<Candle> | null> = new Map()

	private signalHandlers: Map<number, (meta: SignalMeta) => void> = new Map()

	constructor(private ssID: string, private demo: boolean = true) {}

	connect(): Promise<void> {
		return new Promise((resolve, reject) => {
			const endpoint = this.demo ? Region.DEMO_REGION : Region.getRegion()[0]
			const options = Region.SOCKET_OPTIONS
			const auth = extractAuth(this.ssID)

			if (!auth) {
				return reject(new Error('Invalid ssID, authentication failed.'))
			}

			const { session, uid } = auth

			// Set connection timeout
			const timeout = setTimeout(() => {
				if (this.ws) {
					this.ws.disconnect()
				}
				reject(new Error('Connection timeout'))
			}, this.connectionTimeout)

			this.ws = io(endpoint, options)

			this.ws.once('connect', () => {
				if (this.ws) {
					this.ws.emit('auth', {
						isDemo: this.demo ? 1 : 0,
						isFastHistory: true,
						platform: 2,
						session,
						uid
					})
				}
			})

			this.ws.once('successauth', () => {
				clearTimeout(timeout)
				this.isConnected = true
				logger.success(`Broker`, `Authenticated successfully`)

				// Set up balance update listener
				this.setupListeners()

				// Start heartbeat
				this.startHeartbeat()

				// Start candle aggregation
				this.startCandleAggregation()

				resolve()
			})

			// this.ws.on('updateStream', (data: unknown[]) => {})

			// Debug events
			// this.ws.onAny((event: string, ...args: unknown[]) => {
			// 	logger.debug(`Broker`, `Received event: ${event}`)
			// })

			this.ws.on('disconnect', () => {
				this.disconnect()
				logger.warn(`Broker`, `Disconnected from server`)
			})

			this.ws.on('error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Error`, err)
				reject(err)
			})

			this.ws.on('connect_error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Connection error`, err)
				reject(err)
			})
		})
	}

	async emit(event: string, data: unknown): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.emit(event, data)
			resolve()
		})
	}

	async on(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.on(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
			})
			resolve()
		})
	}

	async once(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.once(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
				resolve()
			})
		})
	}

	async getBalance(): Promise<number> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			if (this.balance !== 0) {
				resolve(this.balance)
			} else {
				this.once('successupdateBalance', (data: unknown) => {
					const balanceData = data as BalanceData

					this.balance = balanceData.balance
					resolve(balanceData.balance)
				})
			}
		})
	}

	async placeOrder(payload: OrderPayload): Promise<void> {
		return new Promise<void>(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			Order.init(this)
			await Order.call(payload)

			resolve()
		})
	}

	async getCandles(assetSymbol: string): Promise<unknown[]> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise<unknown[]>(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			Candles.init(this)

			// Wait for chart settings to be available
			const chartSettings = await this.waitForChartSettings()

			// This triggers the event `loadHistoryPeriod` which then responses with `loadHistoryPeriodFast`
			await Candles.call(assetSymbol, chartSettings)

			this.once('loadHistoryPeriodFast', (data: unknown[]) => {
				console.log(JSON.stringify(data)) // Data where asset: 'USDARS_otc', period: 30, data: [...] came from
				resolve(data as unknown[])
			})
		})
	}

	async selectAsset(assetSymbol: string, chartPeriod: number): Promise<{}> {
		return new Promise(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			SelectAsset.init(this)

			this.registerSignalEngine(chartPeriod, new SignalEngine('SMA_CROSS'), meta => {
				console.log(`[SIGNAL] ${chartPeriod}s: ${meta.signal} - ${meta.reason} (${meta.confidence})`)
			})

			// This event, when called will trigger the `updateStream` to start ticking
			await SelectAsset.call(assetSymbol, chartPeriod)
			resolve({ assetSymbol, chartPeriod })
		})
	}

	/**
	 * Check if the broker is connected
	 */
	getConnectionStatus(): boolean {
		return this.isConnected && this.ws?.connected === true
	}

	getChartSettings(): ChartSettings | null {
		logger.debug(`Broker`, `Returning chart settings`)
		return this.chartSettings
	}

	/**
	 * Wait for chart settings to be received from the server
	 * @param timeout Maximum time to wait in milliseconds (default: 1000ms)
	 * @returns Promise that resolves with chart settings
	 */
	async waitForChartSettings(timeout: number = 1000): Promise<ChartSettings> {
		// If chart settings are already available, return them immediately
		if (this.chartSettings) {
			return this.chartSettings
		}

		return new Promise<ChartSettings>((resolve, reject) => {
			const timeoutId = setTimeout(() => {
				reject(new Error('Timeout waiting for chart settings'))
			}, timeout)

			// Set up a listener for chart settings updates
			const checkSettings = () => {
				if (this.chartSettings) {
					clearTimeout(timeoutId)
					resolve(this.chartSettings)
				} else {
					// Check again in 100ms
					setTimeout(checkSettings, 100)
				}
			}

			// Start checking
			checkSettings()
		})
	}

	/**
	 * Disconnect from the broker
	 */
	disconnect(): void {
		if (this.ws) {
			this.stopHeartbeat()
			this.ws.disconnect()
			this.isConnected = false
			logger.info('Broker', 'Disconnected from server')
		}

		if (this.candleFlushTimer) {
			clearInterval(this.candleFlushTimer)
			this.candleFlushTimer = null
		}
	}

	/**
	 * Manually update the balance (useful for testing or external balance updates)
	 */
	updateBalance(newBalance: number): void {
		const oldBalance = this.balance
		this.balance = newBalance
		logger.info('Broker', `Balance manually updated: ${oldBalance} -> ${this.balance}`)
	}

	registerSignalEngine(expirySec: number, engine: SignalEngine, onSignal?: (meta: SignalMeta) => void) {
		const chartPeriodToString = formatChartTimeframe(expirySec)
		if (!chartPeriodToString) {
			throw new Error(`Invalid chart period: ${expirySec}`)
		}

		const expiry = expiryToSeconds(chartPeriodToString)

		logger.debug(`Broker`, `Registered signal engine for ${expirySec}s`)
		this.signalEngines.set(expiry, engine)
		// this.candleBuckets.set(expiry, null)

		if (onSignal) {
			logger.debug(`Broker`, `Registered signal handler for ${expirySec}s`)
			this.signalHandlers.set(expiry, onSignal)
		}
	}

	/**
	 * Set up listener for balance updates
	 */
	private setupListeners(): void {
		if (!this.ws) return

		this.ws.on('updateCharts', this.handleChartSettings)
		this.ws.on('updateStream', this.handleUpdateStream)
	}

	private handleUpdateStream = (data: unknown[]): void => {
		const parsedData = formatData(data) as [string, number, number][]

		for (const [symbol, timestamp, price] of parsedData) {
			// Ensure price is defined before proceeding
			if (typeof price !== 'number') {
				logger.warn(`Broker`, `Invalid price data: ${price} for symbol ${symbol}`)
				continue
			}

			for (const [expiry, engine] of this.signalEngines.entries()) {
				const expirySec = expiryToSeconds(expiry)
				const candleTime = Math.floor(timestamp / expirySec) * expirySec
				let bucket = this.candleBuckets.get(expirySec)

				console.log(`Candle Time: ${candleTime} - Bucket Time: ${bucket?.time} - Timestamp: ${timestamp}`)
				const isNewCandle = !bucket || bucket.time !== candleTime

				if (isNewCandle) {
					if (bucket) {
						logger.success(`Broker`, `Finalized candle for ${expirySec}s: ${JSON.stringify(bucket)}`)

						const finalized: Candle = {
							time: bucket.time!,
							open: bucket.open!,
							high: bucket.high!,
							low: bucket.low!,
							close: bucket.close!
						}

						// if (this.isCandleClosed(finalized, expirySec)) {
						const signal = engine.update(finalized)
						const handler = this.signalHandlers.get(expirySec)
						if (handler) handler(signal)

						logger.info(`SignalEngine [${expirySec}s]`, `${signal.signal} - ${signal.reason}`)
						// }
					}

					// Initialize new bucket
					bucket = {
						time: candleTime,
						open: price,
						high: price,
						low: price,
						close: price
					}
				} else if (bucket) {
					logger.debug(`Broker`, `Updating candle bucket for ${expirySec}s`)
					bucket.high = Math.max(bucket.high, price)
					bucket.low = Math.min(bucket.low, price)
					bucket.close = price
				}

				this.candleBuckets.set(expirySec, bucket)
			}
		}
	}

	private handleChartSettings = (data: unknown[]): void => {
		try {
			const parsedData = formatData(data)

			if (!parsedData) {
				logger.error(`Broker`, `Failed to parse chart settings data - formatData returned null`)
				return
			}

			// Handle different possible data structures
			let chartData: ChartsData | null = null

			if (Array.isArray(parsedData) && parsedData.length > 0) {
				chartData = parsedData[0] as ChartsData
			} else if (typeof parsedData === 'object' && parsedData !== null) {
				chartData = parsedData as ChartsData
			}

			if (!chartData) {
				logger.error(`Broker`, `Invalid chart settings data structure:`, parsedData)
				return
			}

			// Parse the settings based on their type
			if (chartData.settings) {
				if (typeof chartData.settings === 'object') {
					this.chartSettings = chartData.settings as ChartSettings
				} else if (typeof chartData.settings === 'string') {
					try {
						this.chartSettings = JSON.parse(chartData.settings) as ChartSettings
					} catch (parseError) {
						logger.error(`Broker`, `Failed to parse chart settings JSON:`, parseError)
						return
					}
				}
			}

			if (!this.chartSettings) {
				logger.error(`Broker`, `Failed to extract chart settings from data:`, chartData)
				return
			}

			logger.success(`Broker`, `Successfully received and parsed chart settings`)
		} catch (error) {
			logger.error(`Broker`, `Error in handleChartSettings:`, error)
		}
	}

	private startHeartbeat(): void {
		if (!this.ws) return

		// Send a ping every 20 seconds
		this.heartbeatInterval = setInterval(() => {
			if (this.ws) {
				this.ws.emit('ps')
			}
		}, 20000)
	}

	private stopHeartbeat(): void {
		if (!this.heartbeatInterval) return

		clearInterval(this.heartbeatInterval)
		this.heartbeatInterval = null
	}

	private startCandleAggregation(): void {
		if (this.candleFlushTimer) return

		this.candleFlushTimer = setInterval(() => {
			if (this.currentCandle) {
				const filled: Candle = {
					time: this.currentCandle.time!,
					open: this.currentCandle.open!,
					high: this.currentCandle.high!,
					low: this.currentCandle.low!,
					close: this.currentCandle.close!
				}
				const signal = this.signalEngine.update(filled)
				logger.info('SignalEngine', `${signal.signal} - ${signal.reason}`)
				this.currentCandle = null
			}
		}, this.candleBufferInterval * 1000)
	}

	// Create or update a candle in the map
	// private createOrUpdateCandle = (symbol: string, timestamp: number, price: number): Candle => {
	// 	const key = symbol
	// 	const time = Math.floor(timestamp / 30) * 30 // if expiry = 30s
	// 	const prev = this.candleMap.get(key)

	// 	if (!prev || prev.time !== time) {
	// 		const newCandle: Candle = { time, open: price, high: price, low: price, close: price }
	// 		this.candleMap.set(key, newCandle)
	// 		return newCandle
	// 	} else {
	// 		prev.high = Math.max(prev.high, price)
	// 		prev.low = Math.min(prev.low, price)
	// 		prev.close = price
	// 		return prev
	// 	}
	// }
	// Check if a candle is closed based on its timestamp and the current time and expiry, default expiry is 30s
	private isCandleClosed = (candle: Candle, expiry: number = 30): boolean => {
		const now = Date.now() / 1000
		return now > candle.time + expiry
	}
}
